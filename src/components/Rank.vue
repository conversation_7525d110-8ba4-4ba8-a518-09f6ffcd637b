<template>
  <div class="rank">
    <div class="rank-title">
      <div class="rank-title-name">地区</div>
      <div>设备总数(台)</div>
    </div>
    <div class="list main-list">
      <TransitionGroup name="rank" tag="div" class="list h-0">
        <div v-for="item in sortedData" :key="item.country" class="rank-list">
          <div class="rank-li">
            <div>{{ item.country }}</div>
            <div>
              <!-- {{ item.count }} -->
              <counter-number :total="item.count" color="#fff" />
            </div>
          </div>
          <div
            class="rank-block"
            :style="{
              width: item.width,
              minWidth: '10px',
            }"
          >
            <div class="glow" />
          </div>
        </div>
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, computed } from "vue";
import CounterNumber from "./CounterNumber.vue";
import { fetchDashboardRegionData } from "../api/dashboard";
import { useRoute } from "vue-router";

// 删除props，不再接收外部数据

const rawData = ref([]); // 原始数据
const listData = ref([]); // 带宽度的展示数据
let timer = null;
const route = useRoute();

const sortedData = computed(() =>
  [...listData.value].sort((a, b) => b.count - a.count)
);

const supplierId = computed(() => {
  return route.params.supplierId || route.query.supplierId;
});

// 获取真实的区域分布数据
async function fetchRegionData() {
  try {
    const res = await fetchDashboardRegionData(supplierId.value);
    if (res && res.code === 0 && res.data && Array.isArray(res.data)) {
      return res.data.map((item) => ({
        country: item.region,
        count: item.count || 0,
      }));
    }
  } catch (error) {
    console.error("获取区域分布数据失败:", error);
  }

  // 如果接口失败，返回默认数据
  return [
    { country: "中国", count: 0 },
    { country: "泰国", count: 0 },
    { country: "马来西亚", count: 0 },
    { country: "土耳其", count: 0 },
    { country: "沙特阿拉伯", count: 0 },
  ];
}

async function updateData() {
  // 直接调用接口获取数据
  const newData = await fetchRegionData();

  rawData.value = newData;
  const max = Math.max(...newData.map((i) => i.count));

  // 重置动画
  listData.value = newData.map((i) => ({ ...i, width: "0%", dataDisplay: 0 }));

  // 延迟一帧触发动画
  nextTick(() => {
    listData.value = newData.map((i) => ({
      ...i,
      width: `${((i.count / max) * 90).toFixed(2)}%`,
    }));
  });
}

onMounted(() => {
  updateData();
  timer = setInterval(updateData, 30 * 60 * 1000); // 每30分钟刷新一次
});

onBeforeUnmount(() => {
  clearInterval(timer);
});
</script>

<style lang="less" scoped>
.rank {
  width: 100%;
  height: 100%;
  padding: 0 2px;
  display: flex;
  flex-direction: column;

  .rank-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: var(--height-xs);
    margin-bottom: var(--spacing-v-sm);
    flex-shrink: 0;
    .rank-title-name {
      padding-left: 2%;
    }
  }

  .list {
    flex: 1;
    display: flex;
    flex-direction: column;
    // justify-content: space-around;
    // justify-content: space-between;
  }
  .main-list {
    padding-bottom: 4px;
  }
  .rank-list {
    background: linear-gradient(270deg, #005a9b 0%, #001733 100%);
    position: relative;
    border-radius: 4px;
    flex: 1;
    display: flex;
    align-items: center;
    max-height: 20%;
    // margin-bottom: var(--spacing-v-xs);

    &:last-child {
      margin-bottom: 0;
    }
    &::after {
      display: block;
      content: "";
      position: absolute;
      width: calc(100% + 2px);
      height: calc(100% + 2px);
      left: -1px;
      top: -1px;
      border-radius: 4px;
      background: linear-gradient(
        139deg,
        rgba(0, 52, 95, 1),
        rgba(0, 220, 235, 1)
      );
      z-index: -2;
    }
    & + .rank-list {
      margin-top: var(--spacing-v-sm);
    }
  }

  .rank-li {
    width: 100%;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1px 2%;
    border-radius: 8px;
    line-height: var(--height-xs);
    height: var(--height-xs);
    position: relative;
    z-index: 2;
    flex-shrink: 0; /* 不允许收缩 */
    :deep(.num) {
      font-size: var(--font-md);
    }
  }
  .rank-block {
    position: absolute;
    inset: 2px;
    height: calc(100% - 4px);
    background: linear-gradient(270deg, #00da85 0%, #005a9b 100%);
    border-radius: 4px;
    z-index: 0;
    transition: width 1.2s ease-out;
    overflow: hidden;
    &::before {
      display: block;
      content: "";
      position: absolute;
      width: calc(100% - 2px);
      height: calc(100% - 2px);
      left: 1px;
      top: 1px;
      border-radius: 4px;
      background: linear-gradient(270deg, #00da85 0%, #005a9b 100%);
      z-index: -1;
    }
    &::after {
      display: block;
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 220, 235, 1),
        rgba(0, 182, 211, 1)
      );
      z-index: -2;
    }
  }
  .glow {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    opacity: 0.6;
    animation: shimmer 2s linear infinite;
    pointer-events: none;
    border-radius: 4px;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}
.rank-move {
  transition: transform 0.6s ease;
}
</style>
