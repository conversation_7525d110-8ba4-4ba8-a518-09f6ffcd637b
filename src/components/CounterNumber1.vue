<template>
  <div class="num" :style="{ color: color }">
    {{ displayedValue }}
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";

const props = defineProps({
  autoTime: {
    type: Number,
    default: 1000,
  },
  total: {
    type: Number,
    default: 0,
  },
  color: {
    type: String,
    default: "#6ae6ff",
  },
});
const displayedValue = ref(0);
let animationFrame = null;
let timer = null;

// 获取数值的小数位数
function getDecimalPlaces(value) {
  const str = value.toString();
  const decimalIndex = str.indexOf(".");
  if (decimalIndex === -1) return 0;

  const decimalPart = str.substring(decimalIndex + 1);
  // 限制最大小数位数为2
  return Math.min(decimalPart.length, 2);
}

// 格式化数值函数
function formatNumber(value) {
  const decimalPlaces = getDecimalPlaces(props.total);
  const formatted = value.toFixed(decimalPlaces);

  // 如果小数部分为0，则返回整数格式
  if (decimalPlaces > 0 && formatted.endsWith(".00")) {
    return parseFloat(formatted.replace(".00", ""));
  }
  // 移除末尾的0（如 123.10 变为 123.1）
  return parseFloat(formatted);
}

// 模拟接口请求
async function fetchValueFromAPI() {
  // 用真实接口替换此处
  return Math.floor(Math.random() * 10000);
}

function animateToTarget() {
  cancelAnimationFrame(animationFrame);
  const duration = 1500; // 动画时长 1s
  const startTime = performance.now();
  const startValue = displayedValue.value;
  const endValue = props.total;
  const diff = endValue - startValue;

  function step(currentTime) {
    const progress = Math.min((currentTime - startTime) / duration, 1);
    displayedValue.value = formatNumber(startValue + diff * progress);

    if (progress < 1) {
      animationFrame = requestAnimationFrame(step);
    }
  }

  animationFrame = requestAnimationFrame(step);
}
const updateData = () => {
  animateToTarget();
  timer = setInterval(animateToTarget, 5000);
};

watch(
  () => props.total,
  () => {
    updateData();
  }
);

onMounted(() => {});

onBeforeUnmount(() => {
  clearInterval(timer);
  cancelAnimationFrame(animationFrame);
});
</script>

<style scoped>
.num {
  font-size: var(--font-2xl); /* 响应式字体 */
  /* color: #6ae6ff; */
  font-weight: 500;
  text-align: center;
  line-height: var(--line-height-tight); /* 响应式行高 */
}
</style>
