* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background-color: #030929;
  color: #fff;
  overflow: hidden;
}

.chart-container {
  width: 100%;
  height: 100%;
  /* padding: 10px; */
  /* background-color: rgba(0, 20, 80, 0.3); */
  border-radius: 8px;
  /* box-shadow: 0 0 10px rgba(0, 200, 255, 0.2); */
}
.container-box{
  width: 100%;
  height: 100%;
  padding: 10px;
  /* background-color: rgba(0, 20, 80, 0.3); */
  border-radius: 8px;
  /* box-shadow: 0 0 10px rgba(0, 200, 255, 0.2); */
  padding: 1px;
  position: relative;
  background: url(../chart-border-bg.png) no-repeat center center;
  background-size: 100% 100%;
}

.chart-title {
  font-size: 16px;
  color: #A6F9FF;
  /* margin-bottom: 10px; */
  text-align: center;
}

.chart {
  width: 100%;
  height: calc(100% - 30px);
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}